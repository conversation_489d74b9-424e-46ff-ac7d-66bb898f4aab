import { User, VideoConferencing } from "../../../apiEndPoints";
import { logger } from "../../../utils";
import APIrequest, {AppAPIRequest} from "../../axios";


export const VideoConferenceService = {
  /**
   *
   * @returns
   * Function To handle Login action
   */

  addMeetingService: async (bodyData) => {
    try {
      const payload = {
        ...VideoConferencing.addMeeting,
        bodyData,
      };
      const res = await APIrequest(payload);
      return res;
    } catch (error) {
      logger(error);
      throw error;
    }
  },
  meetingListService: async ({ queryParams }) => {
    try {
      const payload = {
        ...VideoConferencing.meetingList,
        queryParams,
      };
      const res = await APIrequest(payload);
      return res;
    } catch (error) {
      logger(error);
      throw error;
    }
  },
  updatePlanMeetingsService: async (id, bodyData) => {
    try {
      const payload = {
        ...VideoConferencing.updatePlanMeetings(id),
        bodyData,
      };
      const res = await APIrequest(payload);
      return res;
    } catch (error) {
      logger(error);
      throw error;
    }
  },
  meetingDetailService: async (id, { queryParams }) => {
    try {
      const payload = {
        ...VideoConferencing.meetingRoomDetail(id),
        queryParams,
      };
      const res = await APIrequest(payload);
      return res;
    } catch (error) {
      logger(error);
      throw error;
    }
  },
  manageAttendanceService: async (bodyData) => {
    try {
      const payload = {
        ...VideoConferencing.manageAttendance,
        bodyData,
      };
      const res = await APIrequest(payload);
      return res;
    } catch (error) {
      logger(error);
      throw error;
    }
  },
  updateManageAttendanceService: async (bodyData, id) => {
    try {
      const payload = {
        ...VideoConferencing.updateManageAttendance(id),
        bodyData,
      };
      const res = await APIrequest(payload);
      return res;
    } catch (error) {
      logger(error);
      throw error;
    }
  },
  VerifyMeetingService: async (bodyData, id) => {
    try {
      const payload = {
        ...VideoConferencing.verifyMeeting(id),
        bodyData,
      };
      const res = await APIrequest(payload);
      return res;
    } catch (error) {
      logger(error);
      throw error;
    }
  },
  meetingDetailIdService: async (id) => {
    try {
      const payload = {
        ...VideoConferencing.meetingDetail(id),
      };
      const res = await APIrequest(payload);
      return res;
    } catch (error) {
      logger(error);
      throw error;
    }
  },
  deleteRecordingService: async (id) => {
    try {
      const payload = {
        ...VideoConferencing.deleteRecording(id),
      };
      const res = await APIrequest(payload);
      return res;
    } catch (error) {
      logger(error);
      throw error;
    }
  },
  getRecordingLinksService: async (id)=>{
    try {
      const payload = {
        ...VideoConferencing.getRecordingLinks(id),
      };
      const res = await AppAPIRequest(payload);
      return res;
    } catch (error) {
      logger(error);
      throw error;
    }
  },
  regionAllocatorService: async () => {
    try {
      const payload = {
        ...User.regionAllocator,
      };
      const res = await APIrequest(payload);
      return res;
    } catch (error) {
      logger(error);
      throw error;
    }
  },
  getUserRecordingLinksService: async (params) => {
    try {
      const payload = {
        ...VideoConferencing.getUserRecordingLinks(),
        queryParams: params,
      };
      const res = await APIrequest(payload);
      return res;
    } catch (error) {
      logger(error);
      throw error;
    }
  },
  getRecordingLinksOfAMeetingService: async (id) => {
    try {
      const payload = {
        ...VideoConferencing.getRecordingLinksOfAMeeting(id),
      };
      const res = await AppAPIRequest(payload);
      return res;
    } catch (error) {
      logger(error);
      throw error;
    }
  },
  getTranscriptionDataService: async (meetingId, recordingId) => {
    try {
      const payload = {
        ...VideoConferencing.getTranscriptionData(meetingId, recordingId),
      };
      const res = await AppAPIRequest(payload);
      return res;
    } catch (error) {
      logger(error);
      throw error;
    }
  },
  getParticipantsLogsService: async (meetingId, sessionId) => {
    try {
      const payload = {
        ...VideoConferencing.getParticipantsLogs(meetingId, sessionId),
      };
      const res = await APIrequest(payload);
      return res;
    } catch (error) {
      logger(error);
      throw error;
    }
  },
  getAttendanceReportService: async (meetingId) => {
    try {
      const payload = {
        ...VideoConferencing.getAttendanceReport(meetingId),
      };
      const res = await APIrequest(payload);
      return res;
    } catch (error) {
      logger(error);
      throw error;
    }
  },
  startTranscriptionAnalysisService: async (bodyData) => {
    try {
      const payload = {
        ...VideoConferencing.startTranscriptionAnalysis(),
        bodyData: {
          meeting_id: bodyData,
        },
      };
      const res = await AppAPIRequest(payload);
      return res;
    } catch (error) {
      logger(error);
      throw error;
    }
  },
  deleteMeetingRecordingService: async (id) => {
    try {
      const payload = {
        ...VideoConferencing.deleteMeetingRecording(),
        bodyData: {
          meeting_ids: id,
        },
      };
      const res = await APIrequest(payload);
      return res;
    } catch (error) {
      logger(error);
      throw error;
    }
  },
  deleteSubRecordingService: async (id) => {
    try {
      const payload = {
        ...VideoConferencing.deleteSubRecording(),
        bodyData: {
          recording_ids: [id],
        },
      };
      const res = await APIrequest(payload);
      return res;
    } catch (error) {
      logger(error);
      throw error;
    }
  },
  getPersonalMeetingRoomIdService: async () => {
    try {
      const payload = {
        ...VideoConferencing.getPersonalMeetingRoomId(),
      };
      const res = await AppAPIRequest(payload);
      return res;
    } catch (error) {
      logger(error);
      throw error;
    }
  },
};
