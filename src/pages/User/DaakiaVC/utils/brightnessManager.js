import { useRef, useCallback, useEffect } from 'react';

/**
 * Centralized Brightness Manager Utility
 * Handles all brightness-related functionality including:
 * - RPC method registration/cleanup
 * - Participant brightness state management
 * - Debounced brightness changes
 * - Memory cleanup for disconnected participants
 * - Optimized sending logic for large meetings
 */

class BrightnessManager {
  
  constructor() {
    this.participantBrightness = new Map();
    this.rpcRegistered = false;
    this.unregisterRpc = null;
    this.debounceTimer = null;
    this.cleanupTimer = null;
    this.room = null;
    this.currentBrightness = 100;

    // Token Bucket Anti-Spam (dynamic based on participant count)
    this.tokenBucket = {
      tokens: 5,           // Start with 5 tokens
      maxTokens: 5,        // Will be dynamic
      refillInterval: 10000, // Will be dynamic (10 seconds default)
      refillTimer: null
    };

    this.handleRpcMessage = this.handleRpcMessage.bind(this);
    this.cleanup = this.cleanup.bind(this);
  }

  /**
   * Initialize brightness manager with room instance
   */
  initialize(room) {
    if (!room || this.room === room) return;

    this.room = room;
    this.registerRpcMethod();
    this.startPeriodicCleanup();
    this.updateTokenBucketConfig(); // Set token bucket based on participant count
    this.startTokenRefill();

    console.log('🔧 BrightnessManager initialized');
  }

  /**
   * Get total participant count (remote + local)
   */
  getParticipantCount() {
    if (!this.room) return 0;
    return this.room.remoteParticipants.size + 1; // +1 for local participant
  }

  /**
   * Update token bucket configuration based on participant count
   */
  updateTokenBucketConfig() {
    const participantCount = this.getParticipantCount();

    if (participantCount < 20) {
      // Less than 20 people: 5 tokens, 10 seconds refill
      this.tokenBucket.maxTokens = 5;
      this.tokenBucket.refillInterval = 10000; // 10 seconds
    } else if (participantCount < 50) {
      // 20-49 people: 3 tokens, 20 seconds refill
      this.tokenBucket.maxTokens = 3;
      this.tokenBucket.refillInterval = 20000; // 20 seconds
    } else {
      // 50+ people: 2 tokens, 20 seconds refill
      this.tokenBucket.maxTokens = 2;
      this.tokenBucket.refillInterval = 20000; // 20 seconds
    }

    // Reset tokens to max when config changes
    this.tokenBucket.tokens = this.tokenBucket.maxTokens;

    console.log(`🪙 Token bucket updated for ${participantCount} participants: ${this.tokenBucket.maxTokens} tokens, ${this.tokenBucket.refillInterval/1000}s refill`);

    // Restart refill timer with new interval
    this.restartTokenRefill();
  }

  /**
   * Start token refill timer
   */
  startTokenRefill() {
    if (this.tokenBucket.refillTimer) return; // Already started

    this.tokenBucket.refillTimer = setInterval(() => {
      if (this.tokenBucket.tokens < this.tokenBucket.maxTokens) {
        this.tokenBucket.tokens += 1;
        console.log(`🪙 Token refilled. Available: ${this.tokenBucket.tokens}/${this.tokenBucket.maxTokens}`);
      }
    }, this.tokenBucket.refillInterval);
  }

  /**
   * Restart token refill timer (when config changes)
   */
  restartTokenRefill() {
    if (this.tokenBucket.refillTimer) {
      clearInterval(this.tokenBucket.refillTimer);
      this.tokenBucket.refillTimer = null;
    }
    this.startTokenRefill();
  }

  /**
   * Check if brightness change is allowed (token bucket)
   */
  canSendBrightness() {
    if (this.tokenBucket.tokens > 0) {
      this.tokenBucket.tokens -= 1;
      console.log(`✅ Brightness allowed. Tokens remaining: ${this.tokenBucket.tokens}/${this.tokenBucket.maxTokens}`);
      return true;
    }

    console.warn(`🚫 Brightness rate limited - no tokens available. Next token in ${this.tokenBucket.refillInterval/1000}s`);
    return false;
  }

  /**
   * Register RPC method for receiving brightness updates
   */
  registerRpcMethod() {
    if (!this.room || this.rpcRegistered) {
      return;
    }

    if (this.room.state !== "connected" || !this.room.localParticipant) {
      console.warn('⚠️ Cannot register brightness RPC: room not connected or local participant unavailable');
      return;
    }

    try {
      this.unregisterRpc = this.room.localParticipant.registerRpcMethod(
        "setBrightness",
        this.handleRpcMessage
      );

      this.rpcRegistered = true;
      console.log('✅ Brightness RPC method registered');
    } catch (error) {
      console.error('❌ Failed to register brightness RPC method:', error);
    }
  }

  /**
   * Handle incoming brightness RPC messages
   */
  handleRpcMessage(data) {
    try {
      const payload = JSON.parse(data.payload);
      const { brightness } = payload;

      // Update participant brightness
      this.participantBrightness.set(data.callerIdentity, brightness);

      console.log('✅ Brightness RPC received:', {
        participant: data.callerIdentity,
        brightness
      });

      return "Brightness updated successfully";
    } catch (error) {
      console.error('❌ Error processing brightness RPC:', error);
      return "Error: Failed to update brightness";
    }
  }

  /**
   * Send brightness to all participants with debouncing and optimization
   */
  sendBrightnessToAll(brightness, options = {}) {
    // If not in room, just update local brightness (for prejoin)
    if (!this.room || this.room.state !== "connected") {
      console.log('📝 Setting brightness locally (not in room)');
      this.currentBrightness = brightness;
      return;
    }

    // Token bucket rate limiting check
    if (!this.canSendBrightness()) {
      console.warn('⚠️ Brightness change blocked by rate limiter');
      return;
    }

    const {
      debounceMs = 700, // Changed to 700ms as requested
      skipIfSame = true,
      maxConcurrent = 10
    } = options;

    // Update token bucket config based on current participant count
    this.updateTokenBucketConfig();

    // Clear existing debounce timer
    if (this.debounceTimer) {
      clearTimeout(this.debounceTimer);
    }

    // Skip if brightness hasn't changed
    if (skipIfSame && this.currentBrightness === brightness) {
      return;
    }

    this.currentBrightness = brightness;

    // Debounce the sending
    this.debounceTimer = setTimeout(() => {
      this.executeBrightnessSend(brightness, maxConcurrent);
    }, debounceMs);
  }

  /**
   * Execute brightness send with concurrency control
   */
  async executeBrightnessSend(brightness, maxConcurrent) {
    // Validate room and connection state
    if (!this.room || this.room.state !== "connected" || !this.room.localParticipant) {
      console.warn('⚠️ Cannot send brightness: room not connected or local participant unavailable');
      return;
    }

    if (brightness === 100) {
      console.log('⏭️ Skipping brightness send: default value');
      return;
    }

    const remoteParticipants = Array.from(this.room.remoteParticipants.values());

    if (remoteParticipants.length === 0) {
      console.log('📭 No remote participants to send brightness to');
      return;
    }

    console.log(`🔄 Sending brightness ${brightness}% to ${remoteParticipants.length} participants`);

    // Process participants in batches to avoid overwhelming the network
    const batches = BrightnessManager.createBatches(remoteParticipants, maxConcurrent);

    for (const batch of batches) {
      const promises = batch.map(participant => this.sendToParticipant(participant, brightness));
      // eslint-disable-next-line no-await-in-loop
      await Promise.allSettled(promises);

      // Small delay between batches for large meetings
      if (batches.length > 1) {
        // eslint-disable-next-line no-await-in-loop
        await new Promise(resolve => {
          setTimeout(resolve, 100);
        });
      }
    }
  }

  /**
   * Send brightness to a single participant
   */
  async sendToParticipant(participant, brightness) {
    try {
      // Validate room and local participant
      if (!this.room || !this.room.localParticipant) {
        throw new Error('Room or local participant not available');
      }

      if (!participant || !participant.identity) {
        throw new Error('Participant identity is missing');
      }

      // Check if room is connected
      if (this.room.state !== "connected") {
        throw new Error(`Room not connected (state: ${this.room.state})`);
      }

      const payload = JSON.stringify({
        brightness,
        participantId: this.room.localParticipant.identity,
        timestamp: Date.now()
      });

      const response = await this.room.localParticipant.performRpc({
        destinationIdentity: participant.identity,
        method: "setBrightness",
        payload,
        responseTimeout: 5000
      });

      return { participant: participant.identity, success: true, response };
    } catch (error) {
      console.error(`❌ Failed to send brightness to ${participant?.identity || 'unknown'}:`, error.message);
      return { participant: participant?.identity || 'unknown', success: false, error: error.message };
    }
  }

  /**
   * Send brightness to newly joined participant
   */
  sendToNewParticipant(participant, brightness = this.currentBrightness) {
    // Validate inputs
    if (!participant || !participant.identity) {
      console.warn('⚠️ Cannot send brightness: invalid participant');
      return;
    }

    if (brightness === 100) {
      console.log(`⏭️ Skipping brightness send to ${participant.identity}: default value`);
      return;
    }

    // Validate room state
    if (!this.room || this.room.state !== "connected" || !this.room.localParticipant) {
      console.warn(`⚠️ Cannot send brightness to ${participant.identity}: room not ready`);
      return;
    }

    console.log(`⏰ Scheduling brightness send to new participant ${participant.identity} in 3 seconds`);

    // Delay sending to ensure participant's RPC methods are registered
    setTimeout(() => {
      // Re-validate room state before sending (room might have disconnected)
      if (this.room && this.room.state === "connected" && this.room.localParticipant) {
        this.sendToParticipant(participant, brightness);
      } else {
        console.warn(`⚠️ Room state changed, skipping brightness send to ${participant.identity}`);
      }
    }, 3000);
  }

  /**
   * Get brightness for a specific participant
   */
  getParticipantBrightness(participantId) {
    return this.participantBrightness.get(participantId) || 100;
  }

  /**
   * Get all participant brightness values
   */
  getAllParticipantBrightness() {
    return new Map(this.participantBrightness);
  }

  /**
   * Clean up disconnected participants
   */
  cleanupDisconnectedParticipants() {
    if (!this.room) return;

    const connectedParticipants = new Set(
      Array.from(this.room.remoteParticipants.keys())
    );

    let cleanedCount = 0;
    for (const participantId of this.participantBrightness.keys()) {
      if (!connectedParticipants.has(participantId)) {
        this.participantBrightness.delete(participantId);
        cleanedCount += 1;
      }
    }

    if (cleanedCount > 0) {
      console.log(`🧹 Cleaned up ${cleanedCount} disconnected participants from brightness map`);
    }
  }

  /**
   * Start periodic cleanup of disconnected participants
   */
  startPeriodicCleanup() {
    if (this.cleanupTimer) return;

    this.cleanupTimer = setInterval(() => {
      this.cleanupDisconnectedParticipants();
    }, 30000); // Clean up every 30 seconds
  }

  /**
   * Create batches for concurrent processing
   */
  static createBatches(array, batchSize) {
    const batches = [];
    for (let i = 0; i < array.length; i += batchSize) {
      batches.push(array.slice(i, i + batchSize));
    }
    return batches;
  }

  /**
   * Complete cleanup and destroy manager
   */
  cleanup() {
    // Clear timers
    if (this.debounceTimer) {
      clearTimeout(this.debounceTimer);
      this.debounceTimer = null;
    }

    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer);
      this.cleanupTimer = null;
    }

    // Clear token bucket timer
    if (this.tokenBucket.refillTimer) {
      clearInterval(this.tokenBucket.refillTimer);
      this.tokenBucket.refillTimer = null;
    }

    // Unregister RPC method
    if (this.unregisterRpc && this.rpcRegistered) {
      try {
        this.unregisterRpc();
        console.log('✅ Brightness RPC method unregistered');
      } catch (error) {
        console.error('❌ Error unregistering brightness RPC method:', error);
      }
    }

    // Clear state
    this.participantBrightness.clear();
    this.rpcRegistered = false;
    this.unregisterRpc = null;
    this.room = null;
    this.currentBrightness = 100;

    console.log('🧹 BrightnessManager cleaned up');
  }
}

// Create singleton instance
const brightnessManager = new BrightnessManager();

/**
 * React hook for brightness management
 */
export const useBrightnessManager = (room, brightness) => {
  const managerRef = useRef(brightnessManager);

  // Initialize manager when room is available and connected
  useEffect(() => {
    if (room) {
      // Initialize immediately if already connected
      if (room.state === "connected" && room.localParticipant) {
        managerRef.current.initialize(room);
      } else {
        // Listen for connection state changes
        const handleStateChange = () => {
          if (room.state === "connected" && room.localParticipant) {
            managerRef.current.initialize(room);
          }
        };

        room.on('connected', handleStateChange);

        return () => {
          room.off('connected', handleStateChange);
        };
      }
    }

    return () => {
      // Cleanup on unmount
      managerRef.current.cleanup();
    };
  }, [room]);

  // Handle brightness changes
  const handleBrightnessChange = useCallback((newBrightness) => {
    managerRef.current.sendBrightnessToAll(newBrightness, {
      debounceMs: 500,
      skipIfSame: true,
      maxConcurrent: 10
    });
  }, []);

  // Handle new participant connections
  const handleNewParticipant = useCallback((participant) => {
    managerRef.current.sendToNewParticipant(participant, brightness);
  }, [brightness]);

  return {
    handleBrightnessChange,
    handleNewParticipant,
    getParticipantBrightness: managerRef.current.getParticipantBrightness.bind(managerRef.current),
    getAllParticipantBrightness: managerRef.current.getAllParticipantBrightness.bind(managerRef.current),
    cleanup: managerRef.current.cleanup.bind(managerRef.current)
  };
};

export default brightnessManager;
