import { useRef, useCallback, useEffect } from 'react';

/**
 * Centralized Brightness Manager Utility
 * Handles all brightness-related functionality including:
 * - RPC method registration/cleanup
 * - Participant brightness state management
 * - Debounced brightness changes
 * - Memory cleanup for disconnected participants
 * - Optimized sending logic for large meetings
 */

class BrightnessManager {
  constructor() {
    this.participantBrightness = new Map();
    this.rpcRegistered = false;
    this.unregisterRpc = null;
    this.debounceTimer = null;
    this.cleanupTimer = null;
    this.room = null;
    this.currentBrightness = 100;
    
    // Bind methods to preserve context
    this.handleRpcMessage = this.handleRpcMessage.bind(this);
    this.cleanup = this.cleanup.bind(this);
  }

  /**
   * Initialize brightness manager with room instance
   */
  initialize(room) {
    if (!room || this.room === room) return;
    
    this.room = room;
    this.registerRpcMethod();
    this.startPeriodicCleanup();
    
    console.log('🔧 BrightnessManager initialized');
  }

  /**
   * Register RPC method for receiving brightness updates
   */
  registerRpcMethod() {
    if (!this.room || this.rpcRegistered || this.room.state !== "connected") return;

    try {
      this.unregisterRpc = this.room.localParticipant.registerRpcMethod(
        "setBrightness",
        this.handleRpcMessage
      );
      
      this.rpcRegistered = true;
      console.log('✅ Brightness RPC method registered');
    } catch (error) {
      console.error('❌ Failed to register brightness RPC method:', error);
    }
  }

  /**
   * Handle incoming brightness RPC messages
   */
  handleRpcMessage(data) {
    try {
      const payload = JSON.parse(data.payload);
      const { brightness } = payload;

      // Update participant brightness
      this.participantBrightness.set(data.callerIdentity, brightness);

      console.log('✅ Brightness RPC received:', {
        participant: data.callerIdentity,
        brightness
      });

      return "Brightness updated successfully";
    } catch (error) {
      console.error('❌ Error processing brightness RPC:', error);
      return "Error: Failed to update brightness";
    }
  }

  /**
   * Send brightness to all participants with debouncing and optimization
   */
  sendBrightnessToAll(brightness, options = {}) {
    const { 
      debounceMs = 500,
      skipIfSame = true,
      maxConcurrent = 10 
    } = options;

    // Clear existing debounce timer
    if (this.debounceTimer) {
      clearTimeout(this.debounceTimer);
    }

    // Skip if brightness hasn't changed
    if (skipIfSame && this.currentBrightness === brightness) {
      return;
    }

    this.currentBrightness = brightness;

    // Debounce the sending
    this.debounceTimer = setTimeout(() => {
      this.executeBrightnessSend(brightness, maxConcurrent);
    }, debounceMs);
  }

  /**
   * Execute brightness send with concurrency control
   */
  async executeBrightnessSend(brightness, maxConcurrent) {
    if (!this.room || this.room.state !== "connected" || brightness === 100) {
      return;
    }

    const remoteParticipants = Array.from(this.room.remoteParticipants.values());
    
    if (remoteParticipants.length === 0) {
      console.log('No remote participants to send brightness to');
      return;
    }

    console.log(`🔄 Sending brightness ${brightness}% to ${remoteParticipants.length} participants`);

    // Process participants in batches to avoid overwhelming the network
    const batches = BrightnessManager.createBatches(remoteParticipants, maxConcurrent);

    for (const batch of batches) {
      const promises = batch.map(participant => this.sendToParticipant(participant, brightness));
      // eslint-disable-next-line no-await-in-loop
      await Promise.allSettled(promises);

      // Small delay between batches for large meetings
      if (batches.length > 1) {
        // eslint-disable-next-line no-await-in-loop
        await new Promise(resolve => {
          setTimeout(resolve, 100);
        });
      }
    }
  }

  /**
   * Send brightness to a single participant
   */
  async sendToParticipant(participant, brightness) {
    try {
      if (!participant.identity) {
        throw new Error('Participant identity is missing');
      }

      const payload = JSON.stringify({
        brightness,
        participantId: this.room.localParticipant.identity,
        timestamp: Date.now()
      });

      const response = await this.room.localParticipant.performRpc({
        destinationIdentity: participant.identity,
        method: "setBrightness",
        payload,
        responseTimeout: 5000
      });

      return { participant: participant.identity, success: true, response };
    } catch (error) {
      console.error(`❌ Failed to send brightness to ${participant.identity}:`, error.message);
      return { participant: participant.identity, success: false, error: error.message };
    }
  }

  /**
   * Send brightness to newly joined participant
   */
  sendToNewParticipant(participant, brightness = this.currentBrightness) {
    if (!participant || brightness === 100) return;

    // Delay sending to ensure participant's RPC methods are registered
    setTimeout(() => {
      this.sendToParticipant(participant, brightness);
    }, 3000);
  }

  /**
   * Get brightness for a specific participant
   */
  getParticipantBrightness(participantId) {
    return this.participantBrightness.get(participantId) || 100;
  }

  /**
   * Get all participant brightness values
   */
  getAllParticipantBrightness() {
    return new Map(this.participantBrightness);
  }

  /**
   * Clean up disconnected participants
   */
  cleanupDisconnectedParticipants() {
    if (!this.room) return;

    const connectedParticipants = new Set(
      Array.from(this.room.remoteParticipants.keys())
    );

    let cleanedCount = 0;
    for (const participantId of this.participantBrightness.keys()) {
      if (!connectedParticipants.has(participantId)) {
        this.participantBrightness.delete(participantId);
        cleanedCount += 1;
      }
    }

    if (cleanedCount > 0) {
      console.log(`🧹 Cleaned up ${cleanedCount} disconnected participants from brightness map`);
    }
  }

  /**
   * Start periodic cleanup of disconnected participants
   */
  startPeriodicCleanup() {
    if (this.cleanupTimer) return;

    this.cleanupTimer = setInterval(() => {
      this.cleanupDisconnectedParticipants();
    }, 30000); // Clean up every 30 seconds
  }

  /**
   * Create batches for concurrent processing
   */
  static createBatches(array, batchSize) {
    const batches = [];
    for (let i = 0; i < array.length; i += batchSize) {
      batches.push(array.slice(i, i + batchSize));
    }
    return batches;
  }

  /**
   * Complete cleanup and destroy manager
   */
  cleanup() {
    // Clear timers
    if (this.debounceTimer) {
      clearTimeout(this.debounceTimer);
      this.debounceTimer = null;
    }

    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer);
      this.cleanupTimer = null;
    }

    // Unregister RPC method
    if (this.unregisterRpc && this.rpcRegistered) {
      try {
        this.unregisterRpc();
        console.log('✅ Brightness RPC method unregistered');
      } catch (error) {
        console.error('❌ Error unregistering brightness RPC method:', error);
      }
    }

    // Clear state
    this.participantBrightness.clear();
    this.rpcRegistered = false;
    this.unregisterRpc = null;
    this.room = null;
    this.currentBrightness = 100;

    console.log('🧹 BrightnessManager cleaned up');
  }
}

// Create singleton instance
const brightnessManager = new BrightnessManager();

/**
 * React hook for brightness management
 */
export const useBrightnessManager = (room, brightness) => {
  const managerRef = useRef(brightnessManager);

  // Initialize manager when room is available
  useEffect(() => {
    if (room && room.state === "connected") {
      managerRef.current.initialize(room);
    }

    return () => {
      // Cleanup on unmount
      managerRef.current.cleanup();
    };
  }, [room]);

  // Handle brightness changes
  const handleBrightnessChange = useCallback((newBrightness) => {
    managerRef.current.sendBrightnessToAll(newBrightness, {
      debounceMs: 500,
      skipIfSame: true,
      maxConcurrent: 10
    });
  }, []);

  // Handle new participant connections
  const handleNewParticipant = useCallback((participant) => {
    managerRef.current.sendToNewParticipant(participant, brightness);
  }, [brightness]);

  return {
    handleBrightnessChange,
    handleNewParticipant,
    getParticipantBrightness: managerRef.current.getParticipantBrightness.bind(managerRef.current),
    getAllParticipantBrightness: managerRef.current.getAllParticipantBrightness.bind(managerRef.current),
    cleanup: managerRef.current.cleanup.bind(managerRef.current)
  };
};

export default brightnessManager;
