@mixin record-buttons {
  height: 35px;
  width: 35px;
  border-radius: 4px;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 1.2rem;
}

.recording {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #fff;
  padding: 8px;
  &-headings {
    display: flex;
    flex-direction: column;
    font-weight: bold;
    &-heading {
      font-size: 18px;
    }
    &-duration {
      font-size: 12px;
    }
  }
  &-icons {
    display: flex;
    gap: 1rem;
    &-download {
      @include record-buttons;
      background-color: #13294b;
      color: white;
    }
    &-copy {
      @include record-buttons;
      background-color: #e2e2e2;
      color: black;
    }
  }
}
// -----------------------------------------
$font: "Inter", sans-serif;

.recorded {
  &-meetings {
    &-title {
      span {
        cursor: pointer;
        margin-left: 1rem;
        font-weight: normal;
        font-family: $font;
      }
    }
    &-header {
      display: flex;
      flex-direction: column;
      .recording-table-headings {
        display: flex;
        justify-content: center;
        // width: 1295px;
        min-width: 100%;
        background-color: #f4f8f9;
        padding: 1rem 3.5rem;
        span {
          font-family: $font;
          font-size: 16px;
          color: #414141;
          // &:first-child{
          //   width: 300px;
          // }
          // &:nth-child(2){
          //   width: 230px;
          // }
          // &:nth-child(3){
          //   width: 200px;
          // }
          // &:nth-child(4){
          //   width: 200px;
          // }
          // &:nth-child(5){
          //   width: 100px;
          // }
        }
      }
      &-bottom {
        display: flex;
        justify-content: space-between;
        width: 100%;
        padding: 1rem 0;
        &-left {
          display: flex;
          justify-content: space-between;
          align-items: center;
          gap: 1rem;
          border: 1px solid #3b60e4;
          border-radius: 13px;
          height: 40px;
          padding: 0 1rem;
          &-highlight{
            background-color: transparent;
            box-shadow: 0 0 10px 0 rgba(59, 96, 228, 0.5);
            .checkbox-meetings{
              span{
                border-color: #3b60e4;
              }
            }
            svg{
              color: #3b60e4 !important;
            }
          }
          .checkbox-meetings {
            font-size: 20px;
            color: #a5a5a5;
            span{
              border-width: 2px;
            }
            &:focus {
              outline: none;
            }
            .ant-checkbox-checked {
              .ant-checkbox-inner {
                background-color: #3b60e4;
                border: none;
              }
            }
          }
          .action-icons {
            font-size: 24px;
            color: #a5a5a5;
            cursor: pointer;
            &-delete {
              font-size: 20px;
            }
            &-dots {
              font-size: 20px;
            }
          }
        }
        .date-range {
          border-color: #3b60e4;
          border-radius: 6px;
          position: relative;
          .ant-picker-suffix {
            left: calc(100% - 25px);
            top: 50%;
            transform: translateY(-50%);
          }
        }
      }
      &-top {
        display: flex;
        justify-content: space-between;
        width: 100%;
        border-bottom: 1px solid #bebebe;
        padding-bottom: 1rem;
      }
      .search-meetings {
        input {
          height: 40px;
          border-radius: 13px 0 0 13px;
          border: none;
          background-color: #f6f6f6;
          &::placeholder {
            font-family: $font;
            font-size: 15px;
            color: #888888;
          }
          &:focus {
            box-shadow: none !important;
          }
        }
        .ant-input-group-addon {
          background-color: #f6f6f6;
          border-radius: 0 13px 13px 0;
          button {
            width: 50px;
            height: 40px;
            background-color: #3b60e4;
            border-radius: 13px !important;
            span {
              color: #fefefe;
            }
          }
        }
      }
      &-pages {
        display: flex;
        gap: 7px;
        align-items: center;
        &-count {
          font-family: $font;
          font-size: 15px;
          color: #555555;
        }
        &-arrows {
          display: flex;
          color: #555555;
          gap: 5px;
          svg {
            font-size: 20px;
            cursor: pointer;
          }
        }
      }
      &-left {
        display: flex;
        width: auto;
        gap: 1.3rem;
        justify-content: space-between;
        align-items: center;
        h4 {
          color: #3b60e4;
          font-family: $font;
          font-size: 20px !important;
          margin: 0;
        }
        .dropdown-duration {
          display: flex;
          gap: 5px;
          font-family: $font;
          font-size: 16px;
          color: #555555;
          cursor: pointer;
          svg {
            color: #555555;
          }
          &-open {
            color: #3b60e4;
          }
          &-overlay {
            .ant-dropdown-menu-title-content {
              label {
                display: flex;
              }
            }
          }
        }
      }
    }
  }
}
.recorded-meetings {
  position: relative;
  &-active {
    span {
      color: #14804a !important;
      background-color: #e1fcef !important;
      border-radius: 10px;
      padding: 2px 12px;
    }
  }
  &-unavailable_plan {
    span {
      background-color: #eae0ff !important;
      color: #7e47eb !important;
      border-radius: 10px;
      padding: 2px 12px;
    }
  }
  &-queued {
    span {
      background-color: #ffe6c6 !important;
      color: #ff8e00 !important;
      border-radius: 10px;
      padding: 2px 12px;
    }
  }
  &-in_progress {
    span {
      background-color: #fdfac7 !important;
      color: #ffc700 !important;
      border-radius: 10px;
      padding: 2px 12px;
    }
  }
  &-failed {
    span {
      background-color: #ffe1e1 !important;
      color: #dc0000 !important;
      border-radius: 10px;
      padding: 2px 12px;
    }
  }
  .table-responsive {
    max-height: 400px;
    background-color: #f4f8f9;
    overflow: auto;
    padding: 1rem;
    .meetings-table {
      thead{
        tr{
          th{
            padding: 15px 20px !important;
            &:first-child{
              width: 50px !important;
            }
            // form 3rd child onwards
            &:nth-child(n+3) {
              text-align: center;
            }
          }
        }
      }
      tbody {
        tr {
          th {
            background-color: #f4f8f9;
            font-weight: normal;
            font-family: $font;
            font-size: 16px;
            padding-bottom: 25px;
          }
          td {
            background-color: #fff !important;
            border-bottom: 15px solid #f4f8f9 !important;
            padding: 15px 20px !important;
            font-family: $font !important;
            font-size: 16px !important;
            &:nth-child(n+3) {
              // width: 200px !important;
              text-align: center;
            }
            &:first-child {
              width: 50px !important;
            }
            &:nth-child(2) {
              width: 300px !important;
            }
          }
        }
      }
    }
    &::-webkit-scrollbar {
      width: 7px;
    }
    &::-webkit-scrollbar-thumb {
      background-color: #cecece;
      border-radius: 10px;
    }
    &::-webkit-scrollbar-track {
      background-color: #f4f8f9;
    }
  }
}
.dropdown-duration {
  &-overlay {
    .ant-dropdown-menu-item {
      .ant-dropdown-menu-title-content {
        label {
          display: flex;
        }
      }
    }
  }
}


// Transcription Analysis Page
.recordings{
  &-header{
    &-title{
      display: flex;
    }
  }
}

.personal-meeting-modal-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem 1rem 1.5rem 1rem;
}
.personal-meeting-modal-title {
  font-size: 1.5rem;
  font-weight: bold;
  text-align: center;
  margin-bottom: 1.2rem;
}
.personal-meeting-modal-link-box {
  display: flex;
  align-items: center;
  background: #eaeaea;
  border-radius: 8px;
  padding: 1rem 1.5rem;
  min-width: 320px;
  max-width: 100%;
  gap: 0.75rem;
  justify-content: center;
}
.personal-meeting-modal-link {
  color: #2563eb;
  font-weight: 500;
  word-break: break-all;
  text-decoration: underline;
  font-size: 1.1rem;
}
.personal-meeting-modal-copy {
  display: flex;
  align-items: center;
  cursor: pointer;
  color: #2563eb;
  margin-left: 0.5rem;
  transition: color 0.2s;
}
.personal-meeting-modal-copy:hover {
  color: #174ea6;
}